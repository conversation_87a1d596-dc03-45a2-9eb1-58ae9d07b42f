<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Repositories\RecommendationRepository;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;

/**
 * APIs for content discovery and recommendations
 */
#[Group('Discovery', weight: 2)]
class DiscoveryController extends Controller
{
    public function __construct(
        private RecommendationRepository $recommendationRepository
    ) {}

    /**
     * Get discovery content
     *
     * Returns personalized recommendations, recently played content, top charts, and top artists.
     * The response structure varies based on user authentication status and available data.
     *
     * @response array{
     *     recommendations: array{
     *         source: string,
     *         title: string,
     *         type: string,
     *         items: \App\Http\Resources\Playlist\PlaylistCollection|\Illuminate\Support\Collection|array
     *     },
     *     recently_played: \App\Http\Resources\Channel\ChannelCollection|array,
     *     top_charts: \App\Http\Resources\Article\ArticleCollection|array,
     *     top_artists: \App\Http\Resources\User\ArtistCollection|array
     * }
     */
    public function index(Request $request): array
    {
        $recommendations = $this->recommendationRepository->getUserPlaylistRecommendation();
        $recentlyPlayed = $this->recommendationRepository->recentlyPlayedChannels();
        $topArtists = $this->recommendationRepository->getTopArtists(limit: 5);

        $recommendationsLessSource = [
            'global_popular_guest',
            'global_popular_fallback',
            'no_recommendations_available',
        ];

        $topCharts = [];
        if (! in_array($recommendations['source'], $recommendationsLessSource)) {
            $topCharts = $this->recommendationRepository->getGlobalPopularTracks(limit: 6);
        }

        return [
            'recommendations' => $recommendations,
            'recently_played' => $recentlyPlayed,
            'top_charts' => $topCharts,
            'top_artists' => $topArtists,
        ];
    }
}
