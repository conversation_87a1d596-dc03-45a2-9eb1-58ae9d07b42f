<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Repositories\RecommendationRepository;
use Dedo<PERSON>\Scramble\Attributes\Group;
use Illuminate\Http\Request;

/**
 * APIs for content discovery and recommendations
 */
#[Group('Discovery', weight: 2)]
class DiscoveryController extends Controller
{
    public function __construct(
        private RecommendationRepository $recommendationRepository
    ) {}

    /**
     * Get discovery content
     *
     * Returns personalized recommendations, recently played content, top charts, and top artists.
     *
     * @return array{
     *     recommendations: array{
     *         source: string,
     *         title: string,
     *         type: string,
     *         items: \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     *     },
     *     recently_played: \Illuminate\Http\Resources\Json\AnonymousResourceCollection,
     *     top_charts: \Illuminate\Http\Resources\Json\AnonymousResourceCollection,
     *     top_artists: \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     * }
     */
    public function index(Request $request): array
    {
        $recommendations = $this->recommendationRepository->getUserPlaylistRecommendation();
        $recentlyPlayed = $this->recommendationRepository->recentlyPlayedChannels();
        $topArtists = $this->recommendationRepository->getTopArtists(limit: 5);

        $recommendationsLessSource = [
            'global_popular_guest',
            'global_popular_fallback',
            'no_recommendations_available',
        ];

        $topCharts = [];
        if (! in_array($recommendations['source'], $recommendationsLessSource)) {
            $topCharts = $this->recommendationRepository->getGlobalPopularTracks(limit: 6);
        }

        return [
            'recommendations' => $recommendations,
            'recently_played' => $recentlyPlayed,
            'top_charts' => $topCharts,
            'top_artists' => $topArtists,
        ];
    }
}
