<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Repositories\RecommendationRepository;
use Dedoc\Scramble\Attributes\Group;
use Illuminate\Http\Request;

/**
 * APIs for content discovery and recommendations
 */
#[Group('Discovery', weight: 2)]
class DiscoveryController extends Controller
{
    public function __construct(
        private RecommendationRepository $recommendationRepository
    ) {}

    /**
     * Get discovery content
     *
     * Returns personalized recommendations, recently played content, top charts, and top artists.
     * The response structure varies based on user authentication status and available data.
     *
     * @response {
     *   "recommendations": {
     *     "source": "personalized_playlists",
     *     "title": "Recommendations",
     *     "type": "playlist",
     *     "items": {
     *       "data": [
     *         {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "name": "My Playlist",
     *           "description": "A great playlist",
     *           "is_system_generated": true,
     *           "generation_strategy": "user_based",
     *           "generated_at": "2024-01-01T00:00:00.000000Z",
     *           "user": {
     *             "id": "01234567-89ab-cdef-0123-456789abcdef",
     *             "name": "Artist Name",
     *             "slug": "artist-name",
     *             "role": "singer"
     *           },
     *           "image": {
     *             "id": "01234567-89ab-cdef-0123-456789abcdef",
     *             "url": "https://example.com/image.jpg",
     *             "filename": "image.jpg"
     *           },
     *           "created_at": "2024-01-01T00:00:00.000000Z"
     *         }
     *       ],
     *       "meta": {
     *         "total": 5
     *       }
     *     }
     *   },
     *   "recently_played": {
     *     "data": [
     *       {
     *         "id": "01234567-89ab-cdef-0123-456789abcdef",
     *         "name": "Channel Name",
     *         "description": "Channel description",
     *         "slug": "channel-name",
     *         "type": "album",
     *         "articles_count": 10,
     *         "likes_count": 5,
     *         "comments_count": 2,
     *         "is_liked": false,
     *         "image": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "url": "https://example.com/image.jpg",
     *           "filename": "image.jpg"
     *         },
     *         "artist": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "name": "Artist Name",
     *           "slug": "artist-name",
     *           "role": "singer"
     *         },
     *         "created_at": "2024-01-01T00:00:00.000000Z",
     *         "updated_at": "2024-01-01T00:00:00.000000Z"
     *       }
     *     ],
     *     "meta": {
     *       "total": 3
     *     }
     *   },
     *   "top_charts": {
     *     "data": [
     *       {
     *         "id": "01234567-89ab-cdef-0123-456789abcdef",
     *         "name": "Song Title",
     *         "description": "Song description",
     *         "year": 2024,
     *         "slug": "song-title",
     *         "duration": 180,
     *         "plays_count": 1000,
     *         "likes_count": 50,
     *         "comments_count": 10,
     *         "is_liked": false,
     *         "image": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "url": "https://example.com/image.jpg",
     *           "filename": "image.jpg"
     *         },
     *         "audio": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "url": "https://example.com/audio.mp3",
     *           "filename": "audio.mp3",
     *           "size": 5242880,
     *           "mime_type": "audio/mpeg"
     *         },
     *         "artist": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "name": "Artist Name",
     *           "slug": "artist-name",
     *           "role": "singer"
     *         },
     *         "channel": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "name": "Album Name",
     *           "slug": "album-name",
     *           "type": "album"
     *         },
     *         "genre": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "name": "Pop",
     *           "slug": "pop"
     *         },
     *         "created_at": "2024-01-01T00:00:00.000000Z",
     *         "updated_at": "2024-01-01T00:00:00.000000Z"
     *       }
     *     ],
     *     "meta": {
     *       "total": 6
     *     }
     *   },
     *   "top_artists": {
     *     "data": [
     *       {
     *         "id": "01234567-89ab-cdef-0123-456789abcdef",
     *         "name": "Artist Name",
     *         "slug": "artist-name",
     *         "role": "singer",
     *         "is_followed": false,
     *         "image": {
     *           "id": "01234567-89ab-cdef-0123-456789abcdef",
     *           "url": "https://example.com/image.jpg",
     *           "filename": "image.jpg"
     *         },
     *         "stats": {
     *           "followers_count": 1000,
     *           "channels_count": 5,
     *           "articles_count": 25,
     *           "total_plays": 50000
     *         },
     *         "created_at": "2024-01-01T00:00:00.000000Z",
     *         "updated_at": "2024-01-01T00:00:00.000000Z"
     *       }
     *     ],
     *     "meta": {
     *       "total": 5
     *     }
     *   }
     * }
     *
     * @response scenario="Guest user" {
     *   "recommendations": {
     *     "source": "no_recommendations_available",
     *     "title": "Recommendations",
     *     "type": "playlist",
     *     "items": []
     *   },
     *   "recently_played": [],
     *   "top_charts": [],
     *   "top_artists": []
     * }
     *
     * @response scenario="User with fallback recommendations" {
     *   "recommendations": {
     *     "source": "global_popular_fallback",
     *     "title": "Recommendations",
     *     "type": "playlist",
     *     "items": [
     *       {
     *         "fallback_popular": {
     *           "id": null,
     *           "name": "Popular Right Now",
     *           "description": "Popular tracks you might like.",
     *           "is_system_generated": true,
     *           "generation_strategy": "popular_global_fallback",
     *           "generated_at": "2024-01-01T00:00:00.000000Z",
     *           "articles": {
     *             "data": [
     *               {
     *                 "id": "01234567-89ab-cdef-0123-456789abcdef",
     *                 "name": "Popular Song",
     *                 "description": "A popular song",
     *                 "duration": 180,
     *                 "plays_count": 5000
     *               }
     *             ],
     *             "meta": {
     *               "total": 10
     *             }
     *           }
     *         }
     *       }
     *     ]
     *   },
     *   "recently_played": {
     *     "data": [],
     *     "meta": {
     *       "total": 0
     *     }
     *   },
     *   "top_charts": {
     *     "data": [],
     *     "meta": {
     *       "total": 0
     *     }
     *   },
     *   "top_artists": {
     *     "data": [],
     *     "meta": {
     *       "total": 0
     *     }
     *   }
     * }
     */
    public function index(Request $request): array
    {
        $recommendations = $this->recommendationRepository->getUserPlaylistRecommendation();
        $recentlyPlayed = $this->recommendationRepository->recentlyPlayedChannels();
        $topArtists = $this->recommendationRepository->getTopArtists(limit: 5);

        $recommendationsLessSource = [
            'global_popular_guest',
            'global_popular_fallback',
            'no_recommendations_available',
        ];

        $topCharts = [];
        if (! in_array($recommendations['source'], $recommendationsLessSource)) {
            $topCharts = $this->recommendationRepository->getGlobalPopularTracks(limit: 6);
        }

        return [
            'recommendations' => $recommendations,
            'recently_played' => $recentlyPlayed,
            'top_charts' => $topCharts,
            'top_artists' => $topArtists,
        ];
    }
}
