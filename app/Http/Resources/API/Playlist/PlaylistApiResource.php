<?php

namespace App\Http\Resources\API\Playlist;

use App\Http\Resources\API\User\ArtistApiResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property \App\Models\Playlist\Playlist $resource
 */
class PlaylistApiResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->resource->id,
            'name' => $this->resource->name,
            'description' => $this->resource->description,
            'is_system_generated' => $this->resource->is_system_generated,
            'generation_strategy' => $this->resource->generation_strategy,
            'generated_at' => $this->resource->generated_at?->toISOString(),
            'image' => $this->when($this->resource->image, function () {
                return [
                    'id' => $this->resource->image->id,
                    'url' => $this->resource->image->url,
                    'alt' => $this->resource->image->alt,
                ];
            }),
            'user' => new ArtistApiResource($this->whenLoaded('user')),
            'articles_count' => $this->resource->articles_count ?? 0,
            'created_at' => $this->resource->created_at?->toISOString(),
            'updated_at' => $this->resource->updated_at?->toISOString(),
        ];
    }
}
