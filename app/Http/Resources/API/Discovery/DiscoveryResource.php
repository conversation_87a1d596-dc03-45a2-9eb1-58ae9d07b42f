<?php

namespace App\Http\Resources\API\Discovery;

use App\Http\Resources\API\Article\ArticleCollection;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Http\Resources\API\Playlist\PlaylistCollection;
use App\Http\Resources\API\User\ArtistCollection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Discovery data resource for API responses
 */
class DiscoveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array{
     *     recommendations: array{
     *         source: string,
     *         title: string,
     *         type: string,
     *         items: array<int, \Illuminate\Http\Resources\Json\ResourceCollection|array<string, mixed>>
     *     },
     *     recently_played: ChannelCollection,
     *     top_charts: ArticleCollection,
     *     top_artists: ArtistCollection
     * }
     */
    public function toArray(Request $request): array
    {
        return [
            'recommendations' => [
                'source' => $this->resource['source'] ?? 'unknown',
                'title' => $this->resource['title'] ?? 'Recommendations',
                'type' => $this->resource['type'] ?? 'mixed',
                'items' => $this->transformRecommendationItems($this->resource['items'] ?? []),
            ],
            'recently_played' => new ChannelCollection($this->resource['recently_played'] ?? []),
            'top_charts' => new ArticleCollection($this->resource['top_charts'] ?? []),
            'top_artists' => new ArtistCollection($this->resource['top_artists'] ?? []),
        ];
    }

    /**
     * Transform recommendation items based on their type
     *
     * @param array<int, mixed> $items
     * @return array<int, \Illuminate\Http\Resources\Json\ResourceCollection|array<string, mixed>>
     */
    private function transformRecommendationItems(array $items): array
    {
        return collect($items)->map(function ($item) {
            if (isset($item['type'])) {
                return match ($item['type']) {
                    'channel' => new ChannelCollection($item['items'] ?? []),
                    'article' => new ArticleCollection($item['items'] ?? []),
                    'playlist' => new PlaylistCollection($item['items'] ?? []),
                    default => [
                        'type' => $item['type'],
                        'items' => $item['items'] ?? [],
                        'title' => $item['title'] ?? null,
                        'source' => $item['source'] ?? null,
                    ],
                };
            }

            return $item;
        })->toArray();
    }
}
