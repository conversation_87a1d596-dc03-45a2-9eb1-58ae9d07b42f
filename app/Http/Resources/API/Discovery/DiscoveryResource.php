<?php

namespace App\Http\Resources\API\Discovery;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Discovery data resource for API responses
 */
class DiscoveryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'recommendations' => [
                'source' => $this->resource['recommendations']['source'] ?? 'unknown',
                'title' => $this->resource['recommendations']['title'] ?? 'Recommendations',
                'type' => $this->resource['recommendations']['type'] ?? 'playlist',
                'items' => $this->resource['recommendations']['items'] ?? [],
            ],
            'recently_played' => $this->resource['recently_played'] ?? [],
            'top_charts' => $this->resource['top_charts'] ?? [],
            'top_artists' => $this->resource['top_artists'] ?? [],
        ];
    }
}
