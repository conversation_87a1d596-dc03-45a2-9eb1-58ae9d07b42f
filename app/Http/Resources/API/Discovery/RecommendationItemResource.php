<?php

namespace App\Http\Resources\API\Discovery;

use App\Http\Resources\API\Article\ArticleCollection;
use App\Http\Resources\API\Channel\ChannelCollection;
use App\Http\Resources\API\Playlist\PlaylistCollection;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Recommendation item resource for API responses
 */
class RecommendationItemResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if (!isset($this->resource['type'])) {
            return $this->resource;
        }

        return match ($this->resource['type']) {
            'channel' => [
                'type' => 'channel',
                'data' => (new ChannelCollection($this->resource['items'] ?? []))->toArray($request),
            ],
            'article' => [
                'type' => 'article',
                'data' => (new ArticleCollection($this->resource['items'] ?? []))->toArray($request),
            ],
            'playlist' => [
                'type' => 'playlist',
                'data' => (new PlaylistCollection($this->resource['items'] ?? []))->toArray($request),
            ],
            default => [
                'type' => $this->resource['type'],
                'items' => $this->resource['items'] ?? [],
                'title' => $this->resource['title'] ?? null,
                'source' => $this->resource['source'] ?? null,
            ],
        };
    }
}
