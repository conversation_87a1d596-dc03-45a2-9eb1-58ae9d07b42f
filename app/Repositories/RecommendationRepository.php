<?php

namespace App\Repositories;

use App\Http\Resources\Article\ArticleResource;
use App\Http\Resources\Channel\ChannelResource;
use App\Http\Resources\Playlist\PlaylistResource;
use App\Http\Resources\User\ArtistResource;
use App\Models\Article;
use App\Models\Channel;
use App\Models\Play;
use App\Models\Playlist\Playlist;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class RecommendationRepository
{
    private array $config;

    public function __construct()
    {
        $this->config = config('recommendations');
    }

    /**
     * Fetch and display playlist recommendations for the user.
     *
     * @return array{source: string, playlists: \Illuminate\Support\Collection|\Illuminate\Http\Resources\Json\AnonymousResourceCollection}
     */
    public function getUserPlaylistRecommendation(): array
    {
        $user = Auth::user();
        $source = 'none';
        $responsePlaylists = null; // Initialize to null or an empty collection

        if (! $user) {
            // --- Guest User Logic ---
            $source = 'no_recommendations_available';
            $responsePlaylists = collect();

        } else {
            // --- Logged-in User Logic ---
            $playlists = Playlist::where('user_id', $user->id)
                ->where('is_system_generated', true)
                ->with('image')
                ->orderBy('generated_at', 'desc')
                ->limit($this->config['target_playlist_count'] ?? 12)
                ->get();

            if ($playlists->isNotEmpty()) {
                // --- Use PlaylistResource to transform the collection ---
                // Key the collection by Playlist ID before applying the resource for consistent output
                $responsePlaylists = PlaylistResource::collection($playlists);
                $source = 'personalized_playlists';

            } else {
                // --- Fallback for Logged-in User ---
                $userHistoryIds = Play::where('user_id', $user->id)
                    ->where('created_at', '>=', Carbon::now()->subDays($this->config['popularity_days'] + 1))
                    ->distinct()
                    ->pluck('article_id');

                $popularTracks = $this->getGlobalPopularTracks(
                    $this->config['items_per_playlist'] ?? 10,
                    $userHistoryIds
                );

                if ($popularTracks->count() > 0) {
                    // Manually structure fallback like PlaylistResource output
                    $responsePlaylists = collect([
                        'fallback_popular' => [
                            'id' => null,
                            'name' => 'Popular Right Now',
                            'description' => 'Popular tracks you might like.',
                            'is_system_generated' => true,
                            'generation_strategy' => 'popular_global_fallback',
                            'generated_at' => now()->toIso8601String(),
                            'articles' => $popularTracks,
                        ],
                    ]);
                    $source = 'global_popular_fallback';
                } else {
                    $source = 'no_recommendations_available';
                    $responsePlaylists = collect(); // Ensure empty collection
                }
            }
        }

        return [
            'source' => $source,
            'playlists' => $responsePlaylists,
        ];
    }

    /**
     * Helper function to get globally popular tracks.
     * Returns a Collection of arrays structured like ArticleResource output.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getGlobalPopularTracks(int $limit, ?Collection $excludeIds = null): AnonymousResourceCollection
    {
        $popularityDays = $this->config['popularity_days'] ?? 7;
        $minPlays = $this->config['min_popular_plays'] ?? 5;

        $playersSub = Play::select([
            'article_id',
            DB::raw('COUNT(DISTINCT player_id) as unique_listeners'),
        ])
            ->whereNotNull('player_id')
            ->where('created_at', '>=', Carbon::now()->subDays($popularityDays))
            ->groupBy('article_id');

        $playingsSub = Play::select(['article_id', DB::raw('COUNT(*) as plays_count')])
            ->whereNotNull('article_id')
            ->where('created_at', '>=', Carbon::now()->subDays($popularityDays))
            ->groupBy('article_id');

        $query = Article::select('articles.*')
            ->joinSub($playersSub, 'listeners', function ($join) {
                $join->on('articles.id', '=', 'listeners.article_id');
            })
            ->joinSub($playingsSub, 'playings', function ($join) {
                $join->on('articles.id', '=', 'playings.article_id');
            })
            ->where('listeners.unique_listeners', '>=', 1)
            ->orderByDesc('listeners.unique_listeners')
            ->orderByDesc('playings.plays_count')
            ->limit($limit);

        if ($excludeIds && $excludeIds->isNotEmpty()) {
            $query->whereNotIn('articles.id', $excludeIds);
        }

        // Eager load relationships needed for formatting
        $query->with(ArticleRepository::RELATIONS);

        // Fetch and map results to match ArticleResource structure
        return ArticleResource::collection($query->get());
    }

    /**
     * Fetch unique channels based on the user's recently played articles.
     * channels are ordered by the most recent play of any track within them.
     *
     * @return array|\Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function recentlyPlayedChannels(): array|AnonymousResourceCollection
    {
        $user = Auth::user();

        // Guests cannot have recently played items
        if (! $user) {
            return [];
        }

        $lookbackDays = $this->config['recently_played_albums_lookback_days'] ?? 30;
        $limit = $this->config['recently_played_albums_limit'] ?? 12;
        $cutoffDate = Carbon::now()->subDays($lookbackDays);

        // Find unique album IDs ordered by the last play time of any track in that album
        $orderedAlbumIds = Play::where('plays.player_id', $user->id)
            ->where('plays.created_at', '>=', $cutoffDate)
            ->join('articles', 'plays.article_id', '=', 'articles.id')
            ->whereNotNull('articles.channel_id')
            ->select('articles.channel_id', DB::raw('MAX(plays.created_at) as last_played_at'))
            ->groupBy('articles.channel_id')
            ->orderByDesc('last_played_at')
            ->limit($limit)
            ->pluck('articles.channel_id'); // Get just the ordered IDs

        if ($orderedAlbumIds->isEmpty()) {
            return [];
        }

        // Use findMany which preserves the order of the IDs passed.
        $albums = Channel::findMany($orderedAlbumIds);
        // Eager load relationships needed for display (adjust as needed)
        $albums->load(ChannelRepository::RELATIONS);

        return ChannelResource::collection($albums);
    }

    /**
     * Get top artists based on the number of plays.
     *
     * @return array|\Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function getTopArtists(int $limit = 6): array|AnonymousResourceCollection
    {
        $user = Auth::user();

        if (! $user) {
            return [];
        }

        $monthlyListenersSub = Play::select([
            'user_id',
            DB::raw("TO_CHAR(created_at, 'Month') as play_month"),
            DB::raw('COUNT(DISTINCT player_id) as unique_listeners'),
        ])
            ->whereNotNull('player_id')
            ->groupBy('user_id', 'play_month');

        // --- Step 2: Middle Subqueries ---
        $maxListenersSub = DB::query()
            ->select(
                'user_id',
                DB::raw('MAX(unique_listeners) as max_monthly_listeners')
            )
            ->fromSub($monthlyListenersSub, 'monthly_counts')
            ->groupBy('user_id');

        $totalPlaysSub = Play::select([
            'user_id',
            DB::raw('COUNT(*) as total_plays'),
        ])
            ->whereNotNull('user_id')
            ->groupBy('user_id');

        // --- Step 3: Outer Query ---
        $topArtists = User::artist()
            ->select([
                'users.*',
                'max_listeners.max_monthly_listeners',
                'total_plays_info.total_plays',
            ])
            ->joinSub($maxListenersSub, 'max_listeners', function ($join) {
                $join->on('users.id', '=', 'max_listeners.user_id');
            })
            ->joinSub($totalPlaysSub, 'total_plays_info', function ($join) {
                $join->on('users.id', '=', 'total_plays_info.user_id');
            })
            ->where('max_listeners.max_monthly_listeners', '>=', 1)
            ->orderBy('max_listeners.max_monthly_listeners', 'desc')
            ->orderBy('total_plays_info.total_plays', 'desc')
            ->limit($limit)
            ->get();

        return ArtistResource::collection($topArtists->withRelationshipAutoloading());
    }
}
